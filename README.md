# 葵 (kuí) - Kubernetes UI To Manage Your Cluster In Terminal

Kui is a modern, feature-rich terminal user interface (TUI) for managing Kubernetes clusters. Built with Go and the Charm libraries, it provides an intuitive and efficient way to interact with your Kubernetes resources without leaving the terminal.

![Kui Screenshot](docs/images/kui-screenshot.png)

## Features

- Beautiful terminal UI with intuitive navigation
- Real-time cluster resource monitoring
- Easy resource management (pods, deployments, services, etc.)
- Context switching between multiple clusters
- Keyboard shortcuts for common operations
- Lightweight and fast performance

## Installation

```bash
curl https://kui.liyujun.dev/install.sh | bash
```

Or install from source:

```bash
go install github.com/liyujun-dev/kui@latest
```

## Usage

Simply run:

```bash
kui
```

### Key Bindings

- `q`: Quit
- `←→↑↓`: Navigate
- `Enter`: Select/Expand
- `Esc`: Go back/Close
- `Ctrl+r`: Refresh
- `/`: Search

## Project Structure

```
kui/
├── cmd/           # Command-line entry points
├── internal/      # Internal packages
│   ├── k8s/       # Kubernetes client implementations
│   ├── ui/        # TUI components and views
│   └── config/    # Configuration handling
├── pkg/           # Public API packages
└── docs/          # Documentation
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Workflow

1. Check the issues for tasks to work on
2. Write tests for your changes
3. Ensure your code passes `go vet` and `golint`
4. Update documentation as needed

## License

MIT
